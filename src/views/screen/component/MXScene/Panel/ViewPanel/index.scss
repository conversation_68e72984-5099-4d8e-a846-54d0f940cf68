.tech-panel {
  position: fixed;
  left: 20px;
  top: 100px;
  width: 360px;
  height: calc(100vh - 110px);
  z-index: 100;
  pointer-events: auto;
  color: #fff;
  transition: width 0.3s cubic-bezier(0.25, 0.8, 0.25, 1),
    transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

  // 添加折叠状态样式
  &.collapsed {
    width: 30px;
    transform: translateX(-10px);

    .panel-container {
      opacity: 0;
      visibility: hidden;
      transform: translateX(-20px);
    }

    .collapse-button {
      right: 20px;
    }
  }

  .panel-container {
    width: 100%;
    height: 100%;
    background: #011c46b3;
    border: 1px solid rgba(45, 115, 193, 0.6);
    border-radius: 2px;
    padding: 12px;
    //   display: flex;
    flex-direction: column;
    gap: 15px;
    overflow-y: auto;
    overflow-x: hidden;
    transition: opacity 0.3s cubic-bezier(0.25, 0.8, 0.25, 1),
      visibility 0.3s cubic-bezier(0.25, 0.8, 0.25, 1),
      transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(13, 47, 102, 0.3);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(100, 181, 246, 0.5);
      border-radius: 2px;

      &:hover {
        background: rgba(100, 181, 246, 0.7);
      }
    }
  }

  // 折叠/展开按钮样式
  .collapse-button {
    position: absolute;
    top: 50%;
    right: -2px;
    transform: translateY(-50%);
    width: 22px;
    height: 38px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    z-index: 10;
    overflow: hidden;

    &:hover {
      transform: translateY(-50%) scale(1.05);

      img {
        filter: brightness(1.2);
      }
    }

    &:active {
      transform: translateY(-50%) scale(0.95);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    }

    img {
      width: 22px;
      height: 38px;
      transition: transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1),
        filter 0.3s ease;
      transform: rotate(180deg);

      &.rotated {
        transform: rotate(0);
      }
    }
  }

  .panel-section {
    border-radius: 2px;
    padding: 8px;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background: rgba(17, 149, 255, 0.08);
    margin-bottom: 10px;
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(
        90deg,
        rgba(45, 115, 193, 0) 0%,
        rgba(45, 115, 193, 0.3) 50%,
        rgba(45, 115, 193, 0) 100%
      );
    }

    &.top-section {
      height: 650px;
      // flex: 1;
    }

    &.middle-section {
      flex: 1;
    }

    &.bottom-section {
      height: 280px;
    }

    &.gate-safety-section {
      height: 380px;
    }

    // 天气信息样式
    .weather-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      height: 28px;

      .title {
        font-size: 16px;
        font-weight: 700;
        font-family: AlimamaShuHeiTi;
        color: #8cd2ff;
      }

      .time-select {
        display: flex;
        align-items: center;
        gap: 4px;
        height: 28px;

        :deep(.time-range-select) {
          width: 90px; // 设置固定宽度

          .el-input {
            .el-input__wrapper {
              background: transparent;
              box-shadow: none !important;
              padding: 0;
              height: 28px;

              &.is-focus {
                box-shadow: none !important;
              }

              .el-input__inner {
                color: #8cd2ff;
                font-size: 13px;
                height: 28px;
                line-height: 28px;
                padding: 0;
                border: none;
                background: transparent;
                text-align: center;

                &::placeholder {
                  color: rgba(100, 181, 246, 0.8);
                }
              }

              .el-select__caret {
                color: #8cd2ff;
                font-size: 12px;
                height: 28px;
                line-height: 28px;
                width: 12px;
              }
            }
          }
        }
      }
    }

    .line {
      display: flex;
      justify-content: space-between;
      width: 100%;
      height: 2px;

      .left,
      .right {
        /* flex:0 0 100px; */
        width: 10px;
        height: 2px;
        background-color: #3276b1;
      }

      .center {
        flex: 1;
        height: 2px;
        background-color: #19477a;
      }
    }

    .weather-main {
      display: flex;
      margin-bottom: 0;
      padding: 0;
      height: 85px;

      .current-weather {
        width: 110px;
        text-align: center;
        position: relative;
        z-index: 2;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .rain-info {
          z-index: 2;
          text-align: left;
        }
      }

      .rain-label {
        color: rgba(255, 255, 255, 0.85);
        margin-bottom: 2px;
        font-weight: 500;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        // font-size: 10px;
        font-size: 12px;
      }

      .rain-value {
        margin-top: 0;

        .value {
          font-size: 20px;
          font-weight: bold;
          color: #29b6f6;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
          letter-spacing: 0.5px;
        }

        .unit {
          font-size: 12px;
          margin-left: 2px;
          color: rgba(255, 255, 255, 0.7);
          font-weight: 300;
        }
      }

      .update-time {
        font-size: 10px;
        color: rgba(255, 255, 255, 0.6);
        margin-top: 4px;
        display: flex;
        align-items: center;

        .update-icon {
          width: 10px;
          height: 10px;
          margin-right: 3px;
        }

        span {
          transform: translateY(1px);
        }
      }

      .day-forecast {
        flex: 1;
        display: flex;
        justify-content: space-around;
        padding-left: 10px;
        gap: 4px;

        .forecast-item {
          flex: 1;
          text-align: center;
          padding: 3px 1px;
          position: relative;
          z-index: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: center;
          transition: all 0.3s ease;

          &:not(:last-child)::after {
            content: "";
            position: absolute;
            right: -2px;
            top: 20%;
            height: 60%;
            width: 1px;
            background: rgba(255, 255, 255, 0.15);
          }

          &:hover {
            transform: translateY(-2px);

            .weather-icon-img {
              transform: scale(1.1);
            }
          }

          .day-badge {
            font-size: 9px;
            font-weight: 600;
            color: #fff;
            background: rgba(21, 101, 192, 0.3);
            border-radius: 8px;
            padding: 1px 6px;
            display: inline-block;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            margin-bottom: 4px;
          }

          .weather-icon-container {
            position: relative;
            width: 28px;
            height: 28px;
            margin: 2px auto;

            .weather-icon-img {
              width: 100%;
              height: 100%;
              object-fit: contain;
              position: relative;
              z-index: 1;
              filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.15));
              transition: transform 0.3s ease;
            }
          }

          .temp {
            font-size: 14px;
            font-weight: 700;
            color: #29b6f6;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            margin-top: 2px;
          }
        }
      }
    }

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
      position: relative;
      height: 28px;

      .title {
        font-size: 16px;
        font-weight: 700;
        font-family: AlimamaShuHeiTi;
        color: #8cd2ff;
      }

      .time-select {
        display: flex;
        align-items: center;
        gap: 4px;
        height: 28px;

        :deep(.time-range-select) {
          width: 90px; // 设置固定宽度

          .el-input {
            .el-input__wrapper {
              background: transparent;
              box-shadow: none !important;
              padding: 0;
              height: 28px;

              &.is-focus {
                box-shadow: none !important;
              }

              .el-input__inner {
                color: #8cd2ff;
                font-size: 13px;
                height: 28px;
                line-height: 28px;
                padding: 0;
                border: none;
                background: transparent;
                text-align: center;

                &::placeholder {
                  color: rgba(100, 181, 246, 0.8);
                }
              }

              .el-select__caret {
                color: #8cd2ff;
                font-size: 12px;
                height: 28px;
                line-height: 28px;
                width: 12px;
              }
            }
          }
        }
      }

      .location-info {
        display: flex;
        align-items: center;
        gap: 4px;
        height: 28px;
      }

      .safety-summary {
        display: flex;
        align-items: center;
        gap: 6px;
        height: 28px;

        .status-indicator {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          display: inline-block;
          animation: status-pulse 2s ease-in-out infinite;

          &.status-safe {
            background: #4caf50;
            box-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
          }

          &.status-caution {
            background: #ff9800;
            box-shadow: 0 0 8px rgba(255, 152, 0, 0.6);
          }

          &.status-warning {
            background: #ff5722;
            box-shadow: 0 0 8px rgba(255, 87, 34, 0.6);
          }

          &.status-danger {
            background: #f44336;
            box-shadow: 0 0 8px rgba(244, 67, 54, 0.8);
            animation: danger-blink 1s ease-in-out infinite;
          }
        }

        .status-text {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.9);
          font-weight: 500;
        }
      }
    }

    .section-content {
      position: relative;
      flex: 1;
      overflow: hidden;

      .chart-container {
        height: 100%;
        width: 100%;
        margin-top: 0;
      }

      .river-chart-container {
        height: 200px;
        min-height: 200px;
        margin-top: 10px;
        width: 100%;
      }

      .gate-safety-container {
        height: 100%;
        width: 100%;
      }

      // 现代化球状仪表盘布局
      .modern-gate-layout {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        height: 100%;
        padding: 5px 0;
      }

      .gate-row {
        display: flex;
        justify-content: space-around;
        align-items: center;
        width: 100%;
        gap: 8px;

        &.first-row {
          flex: 1;
          justify-content: space-around;
          padding: 0 8px;
        }

        &.second-row {
          flex: 1;
          justify-content: center;
          gap: 45px;
          padding: 0 25px;
        }
      }

      .modern-gate-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        cursor: pointer;
        position: relative;
      }

      .gate-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        position: relative;
      }

      // 现代化圆形仪表盘 - 重新设计
      .modern-dashboard {
        width: 90px;
        height: 90px;
        border-radius: 50%;
        position: relative;
        overflow: hidden;
        background: radial-gradient(
            circle at 30% 20%,
            rgba(135, 206, 250, 0.15) 0%,
            rgba(100, 181, 246, 0.08) 30%,
            transparent 60%
          ),
          linear-gradient(
            145deg,
            rgba(25, 60, 120, 0.9) 0%,
            rgba(15, 45, 95, 0.95) 40%,
            rgba(8, 30, 70, 0.98) 100%
          );
        border: 3px solid transparent;
        background-clip: padding-box;
        box-shadow:
          0 0 25px rgba(100, 181, 246, 0.4),
          0 8px 25px rgba(0, 0, 0, 0.3),
          inset 0 2px 0 rgba(255, 255, 255, 0.2),
          inset 0 -2px 0 rgba(0, 0, 0, 0.3),
          inset 0 0 20px rgba(100, 181, 246, 0.1);
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);

        // 添加玻璃质感
        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border-radius: 50%;
          background: linear-gradient(
            135deg,
            rgba(255, 255, 255, 0.1) 0%,
            transparent 30%,
            transparent 70%,
            rgba(0, 0, 0, 0.1) 100%
          );
          z-index: 1;
        }

        // 悬停效果增强
        &:hover {
          transform: scale(1.05);
          box-shadow:
            0 0 35px rgba(100, 181, 246, 0.6),
            0 12px 30px rgba(0, 0, 0, 0.4),
            inset 0 2px 0 rgba(255, 255, 255, 0.25),
            inset 0 -2px 0 rgba(0, 0, 0, 0.35),
            inset 0 0 25px rgba(100, 181, 246, 0.15);
        }
      }

      // 外圈进度环
      .dashboard-ring {
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        border-radius: 50%;
        z-index: -1;
      }

      .ring-progress {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        position: relative;
        background: conic-gradient(
          from -90deg,
          var(--ring-color, #64b5f6) 0deg,
          var(--ring-color, #64b5f6) calc(var(--progress, 0%) * 3.6deg),
          rgba(255, 255, 255, 0.1) calc(var(--progress, 0%) * 3.6deg),
          rgba(255, 255, 255, 0.1) 360deg
        );

        &::before {
          content: "";
          position: absolute;
          top: 50%;
          left: 50%;
          width: 85%;
          height: 85%;
          transform: translate(-50%, -50%);
          background: radial-gradient(
              circle at 35% 25%,
              rgba(120, 200, 255, 0.12) 0%,
              transparent 45%
            ),
            linear-gradient(
              135deg,
              rgba(18, 52, 98, 0.85) 0%,
              rgba(12, 35, 75, 0.9) 50%,
              rgba(8, 25, 58, 0.95) 100%
            );
          border-radius: 50%;
        }

        &::after {
          content: "";
          position: absolute;
          top: 2px;
          left: 2px;
          right: 2px;
          bottom: 2px;
          background: conic-gradient(
            from -90deg,
            rgba(255, 255, 255, 0.3) 0deg,
            rgba(255, 255, 255, 0.3) calc(var(--progress, 0%) * 3.6deg),
            transparent calc(var(--progress, 0%) * 3.6deg)
          );
          border-radius: 50%;
          animation: gentle-ring-glow 3s ease-in-out infinite alternate;
          opacity: 0.6;
        }
      }

      // 水位容器
      .water-container {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        top: 0;
        border-radius: 50%;
        overflow: hidden;
      }

      .water-fill {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        border-radius: 0 0 50% 50%;
        transition: height 1s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;

        // 添加水面扰动效果
        &::before {
          content: "";
          position: absolute;
          top: -20px;
          left: -20%;
          width: 140%;
          height: 25px;
          background: radial-gradient(
            ellipse at center,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 30%,
            transparent 70%
          );
          animation: water-disturbance 4s ease-in-out infinite;
          z-index: 2;
        }

        &.risk-low {
          background: linear-gradient(
            to top,
            #4caf50 0%,
            #66bb6a 25%,
            #81c784 50%,
            #a5d6a7 75%,
            #c8e6c9 100%
          );

          &::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
              45deg,
              transparent 0%,
              rgba(255, 255, 255, 0.2) 20%,
              transparent 40%,
              rgba(255, 255, 255, 0.1) 60%,
              transparent 80%
            );
            animation: water-light-play 6s ease-in-out infinite;
          }
        }

        &.risk-medium {
          background: linear-gradient(
            to top,
            #ff9800 0%,
            #ffb74d 25%,
            #ffcc80 50%,
            #ffe0b2 75%,
            #fff3e0 100%
          );

          &::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
              45deg,
              transparent 0%,
              rgba(255, 255, 255, 0.2) 20%,
              transparent 40%,
              rgba(255, 255, 255, 0.1) 60%,
              transparent 80%
            );
            animation: water-light-play 6s ease-in-out infinite;
          }
        }

        &.risk-high {
          background: linear-gradient(
            to top,
            #ff5722 0%,
            #ff7043 25%,
            #ff8a65 50%,
            #ffab91 75%,
            #ffccbc 100%
          );

          &::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
              45deg,
              transparent 0%,
              rgba(255, 255, 255, 0.2) 20%,
              transparent 40%,
              rgba(255, 255, 255, 0.1) 60%,
              transparent 80%
            );
            animation: water-light-play 6s ease-in-out infinite;
          }
        }

        &.risk-danger {
          background: linear-gradient(
            to top,
            #f44336 0%,
            #ef5350 25%,
            #e57373 50%,
            #ef9a9a 75%,
            #ffcdd2 100%
          );

          &::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
              45deg,
              transparent 0%,
              rgba(255, 255, 255, 0.2) 20%,
              transparent 40%,
              rgba(255, 255, 255, 0.1) 60%,
              transparent 80%
            );
            animation: water-light-play 5s ease-in-out infinite,
              water-agitation 2s ease-in-out infinite;
          }
        }
      }

      // 超明显的水面波纹效果 - 重新设计
      .water-surface-modern {
        position: absolute;
        top: -12px;
        left: 0;
        right: 0;
        height: 24px;
        overflow: hidden;
        z-index: 10;
      }

      .wave {
        position: absolute;
        top: 0;
        left: -100%;
        width: 200%;

        &.wave-1 {
          height: 16px;
          top: 0;
          background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 32'%3E%3Cpath d='M0,16 Q25,4 50,16 T100,16 T150,16 T200,16 V32 H0 Z' fill='%23ffffff' fill-opacity='0.95'/%3E%3C/svg%3E")
            repeat-x;
          background-size: 100px 16px;
          animation: realistic-wave-1 2.5s ease-in-out infinite;
          z-index: 8;
          filter: blur(0.5px);
        }

        &.wave-2 {
          height: 14px;
          top: 2px;
          background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 180 28'%3E%3Cpath d='M0,14 Q22.5,2 45,14 T90,14 T135,14 T180,14 V28 H0 Z' fill='%23ffffff' fill-opacity='0.8'/%3E%3C/svg%3E")
            repeat-x;
          background-size: 90px 14px;
          animation: realistic-wave-2 3s ease-in-out infinite reverse;
          z-index: 7;
          filter: blur(0.3px);
        }
      }

      // 第三层波浪 - 更自然的波形
      .wave-3 {
        position: absolute;
        top: 4px;
        left: -120%;
        width: 240%;
        height: 12px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 160 24'%3E%3Cpath d='M0,12 Q20,1 40,12 T80,12 T120,12 T160,12 V24 H0 Z' fill='%23ffffff' fill-opacity='0.65'/%3E%3C/svg%3E")
          repeat-x;
        background-size: 80px 12px;
        animation: realistic-wave-3 3.5s ease-in-out infinite;
        z-index: 6;
        filter: blur(0.2px);
      }

      // 第四层波浪 - 细微波动
      .wave-4 {
        position: absolute;
        top: 6px;
        left: -150%;
        width: 300%;
        height: 10px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 140 20'%3E%3Cpath d='M0,10 Q17.5,0.5 35,10 T70,10 T105,10 T140,10 V20 H0 Z' fill='%23ffffff' fill-opacity='0.5'/%3E%3C/svg%3E")
          repeat-x;
        background-size: 70px 10px;
        animation: realistic-wave-4 4s ease-in-out infinite reverse;
        z-index: 5;
      }

      // 第五层波浪 - 最细微的波动
      .wave-5 {
        position: absolute;
        top: 8px;
        left: -180%;
        width: 360%;
        height: 8px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 120 16'%3E%3Cpath d='M0,8 Q15,0 30,8 T60,8 T90,8 T120,8 V16 H0 Z' fill='%23ffffff' fill-opacity='0.35'/%3E%3C/svg%3E")
          repeat-x;
        background-size: 60px 8px;
        animation: realistic-wave-5 4.5s ease-in-out infinite;
        z-index: 4;
      }

      // 增强的水面反光效果 - 更真实
      .water-reflection {
        position: absolute;
        top: -4px;
        left: 10%;
        width: 80%;
        height: 6px;
        background: linear-gradient(
          90deg,
          transparent 0%,
          rgba(255, 255, 255, 0.2) 15%,
          rgba(255, 255, 255, 0.6) 25%,
          rgba(255, 255, 255, 0.9) 40%,
          rgba(255, 255, 255, 1) 50%,
          rgba(255, 255, 255, 0.9) 60%,
          rgba(255, 255, 255, 0.6) 75%,
          rgba(255, 255, 255, 0.2) 85%,
          transparent 100%
        );
        border-radius: 50%;
        animation: realistic-water-shimmer 2.2s ease-in-out infinite alternate;
        z-index: 9;
        box-shadow: 0 0 8px rgba(255, 255, 255, 0.4);
        filter: blur(0.5px);
      }

      // 增强的水面涟漪效果 - 更自然
      .water-ripples-surface {
        position: absolute;
        top: -6px;
        left: 0;
        right: 0;
        height: 12px;

        .ripple-circle {
          position: absolute;
          border: 1.5px solid rgba(255, 255, 255, 0.7);
          border-radius: 50%;
          animation: realistic-surface-ripple 3s ease-out infinite;

          &:nth-child(1) {
            width: 12px;
            height: 6px;
            left: 20%;
            top: 2px;
            animation-delay: 0s;
          }

          &:nth-child(2) {
            width: 16px;
            height: 8px;
            left: 50%;
            top: 1px;
            animation-delay: 1.2s;
          }

          &:nth-child(3) {
            width: 10px;
            height: 5px;
            left: 75%;
            top: 3px;
            animation-delay: 2.4s;
          }

          &:nth-child(4) {
            width: 14px;
            height: 7px;
            left: 35%;
            top: 2px;
            animation-delay: 0.8s;
          }
        }
      }

      // 中心内容显示
      .dashboard-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        z-index: 10;
      }

      .percentage-display {
        color: #ffffff;
        font-size: 18px;
        font-weight: 800;
        text-shadow: 0 2px 6px rgba(0, 0, 0, 0.8),
          0 0 10px rgba(100, 181, 246, 0.5);
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        margin-bottom: 2px;
      }

      .status-badge {
        padding: 1px 6px;
        border-radius: 10px;
        font-size: 9px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        backdrop-filter: blur(8px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
        white-space: nowrap;
        box-shadow: 0 1px 6px rgba(0, 0, 0, 0.3);

        &.risk-low {
          background: linear-gradient(
            135deg,
            rgba(76, 175, 80, 0.9),
            rgba(102, 187, 106, 0.8)
          );
          color: #ffffff;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
        }

        &.risk-medium {
          background: linear-gradient(
            135deg,
            rgba(255, 152, 0, 0.9),
            rgba(255, 183, 77, 0.8)
          );
          color: #ffffff;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
        }

        &.risk-high {
          background: linear-gradient(
            135deg,
            rgba(255, 87, 34, 0.9),
            rgba(255, 138, 101, 0.8)
          );
          color: #ffffff;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
        }

        &.risk-danger {
          background: linear-gradient(
            135deg,
            rgba(244, 67, 54, 0.9),
            rgba(239, 83, 80, 0.8)
          );
          color: #ffffff;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
          animation: modern-danger-pulse 2s ease-in-out infinite;
        }
      }

      .modern-gate-name {
        color: rgba(255, 255, 255, 0.9);
        font-size: 12px;
        font-weight: 600;
        text-align: center;
        margin-top: 6px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        padding: 3px 6px;
        border-radius: 6px;
        background: rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(4px);
        border: 1px solid rgba(100, 181, 246, 0.15);
      }

      // 悬浮详情
      .hover-details {
        position: absolute;
        top: 105%;
        left: 50%;
        transform: translateX(-50%) translateY(4px);
        background: rgba(0, 25, 60, 0.95);
        border: 1px solid rgba(64, 158, 255, 0.3);
        border-radius: 6px;
        padding: 6px 8px;
        min-width: 110px;
        backdrop-filter: blur(12px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        z-index: 100;

        &::before {
          content: "";
          position: absolute;
          top: -5px;
          left: 50%;
          transform: translateX(-50%);
          width: 0;
          height: 0;
          border-left: 5px solid transparent;
          border-right: 5px solid transparent;
          border-bottom: 5px solid rgba(0, 25, 60, 0.95);
        }
      }

      .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 3px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-size: 9px;
          color: rgba(255, 255, 255, 0.7);
          font-weight: 500;
        }

        .value {
          font-size: 9px;
          color: #64b5f6;
          font-weight: 600;

          &.safety-margin {
            color: #4caf50;
          }
        }
      }

      // 现代化动画关键帧
      @keyframes gentle-ring-glow {
        0% {
          opacity: 0.4;
          transform: scale(1);
        }
        100% {
          opacity: 0.8;
          transform: scale(1.02);
        }
      }

      // 新的真实水波动画
      @keyframes realistic-wave-1 {
        0% {
          transform: translateX(0) scaleY(1);
          opacity: 0.95;
        }
        25% {
          transform: translateX(25px) scaleY(1.3);
          opacity: 1;
        }
        50% {
          transform: translateX(50px) scaleY(0.7);
          opacity: 0.9;
        }
        75% {
          transform: translateX(75px) scaleY(1.1);
          opacity: 1;
        }
        100% {
          transform: translateX(100px) scaleY(1);
          opacity: 0.95;
        }
      }

      @keyframes realistic-wave-2 {
        0% {
          transform: translateX(0) scaleY(0.8);
          opacity: 0.8;
        }
        30% {
          transform: translateX(-27px) scaleY(1.4);
          opacity: 0.9;
        }
        60% {
          transform: translateX(-54px) scaleY(0.6);
          opacity: 0.7;
        }
        100% {
          transform: translateX(-90px) scaleY(0.8);
          opacity: 0.8;
        }
      }

      @keyframes realistic-wave-3 {
        0% {
          transform: translateX(0) scaleY(1);
          opacity: 0.65;
        }
        20% {
          transform: translateX(16px) scaleY(1.2);
          opacity: 0.75;
        }
        40% {
          transform: translateX(32px) scaleY(0.8);
          opacity: 0.6;
        }
        60% {
          transform: translateX(48px) scaleY(1.1);
          opacity: 0.7;
        }
        80% {
          transform: translateX(64px) scaleY(0.9);
          opacity: 0.65;
        }
        100% {
          transform: translateX(80px) scaleY(1);
          opacity: 0.65;
        }
      }

      @keyframes realistic-wave-4 {
        0% {
          transform: translateX(0) scaleY(1);
          opacity: 0.5;
        }
        25% {
          transform: translateX(-17.5px) scaleY(1.15);
          opacity: 0.6;
        }
        50% {
          transform: translateX(-35px) scaleY(0.85);
          opacity: 0.45;
        }
        75% {
          transform: translateX(-52.5px) scaleY(1.05);
          opacity: 0.55;
        }
        100% {
          transform: translateX(-70px) scaleY(1);
          opacity: 0.5;
        }
      }

      @keyframes realistic-wave-5 {
        0% {
          transform: translateX(0) scaleY(1);
          opacity: 0.35;
        }
        33% {
          transform: translateX(20px) scaleY(1.1);
          opacity: 0.4;
        }
        66% {
          transform: translateX(40px) scaleY(0.9);
          opacity: 0.3;
        }
        100% {
          transform: translateX(60px) scaleY(1);
          opacity: 0.35;
        }
      }

      @keyframes realistic-water-shimmer {
        0% {
          opacity: 0.4;
          transform: scaleX(0.8) scaleY(1);
        }
        30% {
          opacity: 0.8;
          transform: scaleX(1.2) scaleY(1.1);
        }
        60% {
          opacity: 1;
          transform: scaleX(1.3) scaleY(0.9);
        }
        100% {
          opacity: 0.6;
          transform: scaleX(0.9) scaleY(1);
        }
      }

      @keyframes realistic-surface-ripple {
        0% {
          opacity: 0;
          transform: scale(0.5);
        }
        15% {
          opacity: 0.8;
          transform: scale(1);
        }
        30% {
          opacity: 0.7;
          transform: scale(1.8);
        }
        50% {
          opacity: 0.5;
          transform: scale(2.5);
        }
        70% {
          opacity: 0.3;
          transform: scale(3.2);
        }
        90% {
          opacity: 0.1;
          transform: scale(4);
        }
        100% {
          opacity: 0;
          transform: scale(4.5);
        }
      }

      @keyframes modern-danger-pulse {
        0%,
        100% {
          box-shadow: 0 1px 6px rgba(0, 0, 0, 0.3);
        }
        50% {
          box-shadow: 0 1px 6px rgba(0, 0, 0, 0.3),
            0 0 15px rgba(244, 67, 54, 0.7);
        }
      }

      @keyframes status-pulse {
        0%,
        100% {
          opacity: 1;
          transform: scale(1);
        }
        50% {
          opacity: 0.7;
          transform: scale(1.1);
        }
      }

      @keyframes danger-blink {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }

      @keyframes water-disturbance {
        0%,
        100% {
          transform: translateY(0) scaleX(1);
          opacity: 0.3;
        }
        25% {
          transform: translateY(-3px) scaleX(1.1);
          opacity: 0.6;
        }
        50% {
          transform: translateY(-5px) scaleX(0.9);
          opacity: 0.4;
        }
        75% {
          transform: translateY(-2px) scaleX(1.05);
          opacity: 0.7;
        }
      }

      @keyframes water-light-play {
        0% {
          transform: translateX(-100%) rotate(0deg);
          opacity: 0;
        }
        25% {
          opacity: 0.6;
        }
        50% {
          transform: translateX(0%) rotate(5deg);
          opacity: 0.8;
        }
        75% {
          opacity: 0.4;
        }
        100% {
          transform: translateX(100%) rotate(10deg);
          opacity: 0;
        }
      }

      @keyframes water-agitation {
        0%,
        100% {
          transform: scaleY(1) scaleX(1);
        }
        25% {
          transform: scaleY(1.02) scaleX(0.98);
        }
        50% {
          transform: scaleY(0.98) scaleX(1.02);
        }
        75% {
          transform: scaleY(1.01) scaleX(0.99);
        }
      }

      .update-time-bottom {
        text-align: center;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        margin-top: 5px;
        padding-bottom: 5px;
      }
    }

    // 统一天气卡片样式
    .unified-weather-card {
      background: linear-gradient(
        135deg,
        rgba(21, 101, 192, 0.2),
        rgba(3, 169, 244, 0.05)
      );
      border-radius: 12px;
      margin: 6px 0;
      padding: 8px 10px;
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 -1px 3px rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(100, 181, 246, 0.3);
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(5px);
    }
  }

  // Element Plus表格样式覆盖
  :deep(.rain-data-table) {
    --el-table-header-bg-color: transparent;
    --el-table-bg-color: transparent;
    --el-table-tr-bg-color: transparent;
    --el-table-header-text-color: #fff;
    --el-table-text-color: #fff;

    background-color: transparent;
    color: #fff;
    height: 100%;
    width: 100%;

    .el-table__header {
      background-color: transparent;

      th.el-table__cell {
        background-color: rgba(25, 118, 210, 0.4) !important;
        border-bottom: 1px solid rgba(45, 115, 193, 0.3);
        color: rgba(255, 255, 255, 0.95);
        font-size: 12px;

        .cell {
          color: rgba(255, 255, 255, 0.95);
        }
      }
    }

    .el-table__body {
      background-color: transparent;

      tr {
        background-color: transparent;

        td {
          background-color: transparent;
          color: #fff;

          .cell {
            color: #fff;
          }
        }
      }
    }

    &::before {
      display: none;
    }

    .el-table__inner-wrapper::before {
      display: none;
    }

    .el-table__header-wrapper {
      background-color: transparent;

      th.el-table__cell {
        background-color: rgba(25, 118, 210, 0.4) !important;
        border-bottom: 1px solid rgba(45, 115, 193, 0.3);

        &.is-leaf {
          border-bottom: 1px solid rgba(45, 115, 193, 0.3);
        }
      }
    }

    .el-table__body-wrapper {
      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(33, 150, 243, 0.5);
        border-radius: 2px;
      }

      tr.rain-table-row:hover > td.el-table__cell {
        background-color: rgba(21, 101, 192, 0.1);
      }

      td.el-table__cell {
        background-color: transparent !important;
        border-bottom: 1px solid rgba(45, 115, 193, 0.2);
      }
    }
  }

  // 保留原有的排名样式
  .rank-item {
    width: 24px;
    height: 18px;
    line-height: 18px;
    margin: 0 auto;
    transform: skew(-15deg);
    position: relative;
    overflow: hidden;

    &.rank-1 {
      background: linear-gradient(45deg, #ff4757, #ff6b81);
      color: #fff;
      box-shadow: 0 2px 4px rgba(255, 71, 87, 0.3);
    }

    &.rank-2 {
      background: linear-gradient(45deg, #ffa502, #ffb733);
      color: #fff;
      box-shadow: 0 2px 4px rgba(255, 165, 2, 0.3);
    }

    &.rank-3 {
      background: linear-gradient(45deg, #2ed573, #7bed9f);
      color: #fff;
      box-shadow: 0 2px 4px rgba(46, 213, 115, 0.3);
    }
  }

  .empty-data-container {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px 0;
    height: 100%;
    width: 100%;

    .empty-icon {
      font-size: 40px;
      opacity: 0.9;
      margin-bottom: 12px;

      .el-icon {
        width: 48px;
        height: 48px;
        font-size: 48px;
        color: rgba(100, 181, 246, 0.6);
        transition: color 0.3s ease;
      }
    }

    .empty-text {
      font-size: 14px;
      font-weight: 500;
      color: rgba(200, 220, 255, 0.6);
      letter-spacing: 0.5px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
      transition: color 0.3s ease;
      text-align: center;
      max-width: 80%;
    }
  }
}

:deep(.basin-select),
:deep(.river-select) {
  width: 80px; // 设置固定宽度

  .el-input {
    .el-input__wrapper {
      background: transparent;
      box-shadow: none !important;
      padding: 0;
      height: 28px;

      &.is-focus {
        box-shadow: none !important;
      }

      .el-input__inner {
        color: #8cd2ff;
        font-size: 13px;
        height: 28px;
        line-height: 28px;
        padding: 0;
        border: none;
        background: transparent;
        text-align: center;

        &::placeholder {
          color: rgba(100, 181, 246, 0.8);
        }
      }

      .el-select__caret {
        color: #8cd2ff;
        font-size: 12px;
        height: 28px;
        line-height: 28px;
        width: 12px;
      }

      &:hover {
        background: rgba(21, 101, 192, 0.1);
      }

      &.is-focus {
        background: rgba(21, 101, 192, 0.15);
      }
    }
  }
}
