<!--
 * @Description:
 * @Author: liguiyuan
 * @LastEditors: liguiyuan
-->
<template>
    <div class="tech-panel" :class="{ collapsed: isPanelCollapsed }">
      <div class="panel-container">
        <!-- 上部区域 - 天气信息 -->
        <div class="panel-section top-section">
          <div class="weather-header">
            <div class="title">区域降雨</div>
            <div class="time-select">
              <el-select
                v-model="activeWeatherTimeRange"
                class="time-range-select"
                :popper-class="'dark-select-dropdown'"
                size="small"
                @change="changeWeatherTimeRange"
              >
                <el-option
                  v-for="item in timeRangeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
          <div class="line">
            <div class="left"></div>
            <div class="center"></div>
            <div class="right"></div>
          </div>
          <div class="unified-weather-card">
            <div class="weather-main">
              <div class="current-weather">
                <div class="rain-info">
                  <div class="rain-label">降雨量</div>
                  <div class="rain-value">
                    <span class="value">{{ weatherData.rainfall }}</span>
                    <span class="unit">mm</span>
                  </div>
                  <!-- <div class="update-time">
                      <img
                        :src="updateIcon"
                        class="update-icon"
                        alt="更新时间图标"
                      />
                      <span>{{ currentTime }}</span>
                    </div> -->
                </div>
              </div>

              <div class="day-forecast">
                <div
                  class="forecast-item"
                  v-for="(day, index) in weatherData.forecast"
                  :key="index"
                >
                  <div class="day-badge">{{ day.day }}</div>
                  <div class="weather-icon-container">
                    <img
                      :src="getWeatherIcon(day.weather)"
                      :alt="day.weather"
                      class="weather-icon-img"
                    />
                  </div>
                  <div class="temp">{{ day.temp }}°</div>
                </div>
              </div>
            </div>
          </div>
          <div class="section-content">
            <div class="chart-container" ref="rainChartRef">
              <div class="empty-data-container">
                <div class="empty-icon">
                  <el-icon>
                    <DataLine />
                  </el-icon>
                </div>
                <div class="empty-text">所选时段无降雨</div>
              </div>
            </div>
          </div>
          <!-- </div> -->

          <!-- 中部区域 - 乡镇降雨 -->
          <!-- <div class="panel-section middle-section">
          <div class="section-header">
            <div class="title">乡镇降雨</div>
            <div class="time-select">
              <el-select
                v-model="activeTimeRange"
                class="time-range-select"
                :popper-class="'dark-select-dropdown'"
                size="small"
                @change="changeTimeRange"
              >
                <el-option
                  v-for="item in timeRangeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div> -->
          <!-- <div class="line">
            <div class="left"></div>
            <div class="center"></div>
            <div class="right"></div>
          </div> -->
          <div class="section-content">
            <el-table
              :data="rainData"
              :header-cell-style="{
                fontWeight: '500',
              }"
              :cell-style="{
                textAlign: 'center',
                background: 'transparent',
                color: '#fff',
              }"
              :row-style="{
                background: 'transparent',
              }"
              :hover-row-style="{
                background: 'rgba(21, 101, 192, 0.1)',
              }"
              class="rain-data-table"
              :row-class-name="'rain-table-row'"
              :border="false"
              :highlight-current-row="false"
              style="margin: 10px 0 0 0"
            >
              <template #empty>
                <div class="empty-data-container">
                  <div class="empty-icon">
                    <el-icon>
                      <Document />
                    </el-icon>
                  </div>
                  <div class="empty-text">所选时段无降雨</div>
                </div>
              </template>
              <el-table-column label="序号" width="50" align="center">
                <template #default="{ $index }">
                  <div class="rank-item" :class="`rank-${$index + 1}`">
                    {{ $index + 1 }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="adnm"
                label="乡镇"
                show-overflow-tooltip
                align="center"
              ></el-table-column>
              <el-table-column
                prop="rainfall"
                label="雨量值(mm)"
                width="90"
              ></el-table-column>
              <el-table-column
                prop="rainMax"
                label="最大雨强(mm/h)"
                width="115"
              ></el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 下部区域 - 河流水情 -->
        <div class="panel-section bottom-section">
          <div class="section-header">
            <div class="title">河流水情</div>
            <div class="location-info">
              <el-tree-select
                v-model="selectedBasin"
                @change="handleBasinChange"
                class="basin-select"
                :data="basinOptions"
                :props="{
                  children: 'children',
                  label: 'name',
                  value: 'basinId',
                }"
                :popper-class="'dark-select-dropdown'"
                placeholder="流域"
              />
              <el-select
                v-model="selectedRiver"
                @change="handleRiverChange"
                class="river-select"
                :popper-class="'dark-select-dropdown'"
                size="small"
                placeholder="河流"
              >
                <el-option
                  v-for="item in riverOptions"
                  :key="item.rvCode"
                  :label="item.rvName"
                  :value="item.rvCode"
                />
              </el-select>
            </div>
          </div>
          <div class="line">
            <div class="left"></div>
            <div class="center"></div>
            <div class="right"></div>
          </div>
          <div class="section-content">
            <div class="river-chart-container" ref="riverChartRef"></div>
            <!-- <div class="update-time-bottom">更新时间: {{ currentTime }}</div> -->
          </div>
        </div>

        <!-- 闸门安全区域 -->
        <div class="panel-section gate-safety-section">
          <div class="section-header">
            <div class="title">闸门安全</div>
            <div class="safety-summary">
              <span class="status-indicator" :class="getOverallSafetyStatus()"></span>
              <span class="status-text">{{ getOverallSafetyText() }}</span>
            </div>
          </div>
          <div class="line">
            <div class="left"></div>
            <div class="center"></div>
            <div class="right"></div>
          </div>
                    <div class="section-content">
            <div class="gate-safety-container">
              <div class="modern-gate-layout">
                <!-- 第一行：3个仪表盘 -->
                <div class="gate-row first-row">
                  <div
                    v-for="(gate, index) in gateSafetyData.slice(0, 3)"
                    :key="`first-${index}`"
                    class="modern-gate-item"
                    @click="handleGateClick(gate)"
                  >
                    <div class="gate-container">
                      <!-- 现代化圆形仪表盘 -->
                      <div class="modern-dashboard" :class="getRiskLevelClass(gate.percentage)">
                        <!-- 外圈渐变边框 -->
                        <div class="dashboard-ring">
                          <div class="ring-progress" :style="{
                            '--progress': gate.percentage + '%',
                            '--ring-color': getRiskColor(gate.percentage)
                          }"></div>
                        </div>

                        <!-- 内部水位填充 -->
                        <div class="water-container">
                          <div
                            class="water-fill"
                            :style="{ height: gate.percentage + '%' }"
                            :class="getRiskLevelClass(gate.percentage)"
                          >
                            <!-- 超明显的水面波纹效果 -->
                            <div class="water-surface-modern">
                              <div class="wave wave-1"></div>
                              <div class="wave wave-2"></div>
                              <div class="wave-3"></div>
                              <div class="wave-4"></div>
                              <div class="wave-5"></div>
                              <!-- 增强的水面反光 -->
                              <div class="water-reflection"></div>
                              <!-- 增强的水面涟漪 -->
                              <div class="water-ripples-surface">
                                <div class="ripple-circle"></div>
                                <div class="ripple-circle"></div>
                                <div class="ripple-circle"></div>
                                <div class="ripple-circle"></div>
                              </div>
                            </div>

                          </div>
                        </div>

                        <!-- 中心数据显示 -->
                        <div class="dashboard-content">
                          <div class="percentage-display">{{ gate.percentage }}%</div>
                          <div class="status-badge" :class="getRiskLevelClass(gate.percentage)">
                            {{ getRiskLevelText(gate.percentage) }}
                          </div>
                        </div>

                        <!-- 悬浮详情信息 -->
                        <div class="hover-details">
                          <div class="detail-row">
                            <span class="label">当前</span>
                            <span class="value">{{ gate.currentHeight }}m</span>
                          </div>
                          <div class="detail-row">
                            <span class="label">最大</span>
                            <span class="value">{{ gate.maxHeight }}m</span>
                          </div>
                          <div class="detail-row">
                            <span class="label">余量</span>
                            <span class="value safety-margin">{{ (gate.maxHeight - gate.currentHeight).toFixed(1) }}m</span>
                          </div>
                        </div>
                      </div>

                      <!-- 闸门名称 -->
                      <div class="modern-gate-name">{{ gate.name }}</div>
                    </div>
                  </div>
                </div>

                <!-- 第二行：2个仪表盘 -->
                <div class="gate-row second-row">
                  <div
                    v-for="(gate, index) in gateSafetyData.slice(3, 5)"
                    :key="`second-${index}`"
                    class="modern-gate-item"
                    @click="handleGateClick(gate)"
                  >
                    <div class="gate-container">
                      <!-- 现代化圆形仪表盘 -->
                      <div class="modern-dashboard" :class="getRiskLevelClass(gate.percentage)">
                        <!-- 外圈渐变边框 -->
                        <div class="dashboard-ring">
                          <div class="ring-progress" :style="{
                            '--progress': gate.percentage + '%',
                            '--ring-color': getRiskColor(gate.percentage)
                          }"></div>
                        </div>

                        <!-- 内部水位填充 -->
                        <div class="water-container">
                          <div
                            class="water-fill"
                            :style="{ height: gate.percentage + '%' }"
                            :class="getRiskLevelClass(gate.percentage)"
                          >
                            <!-- 超明显的水面波纹效果 -->
                            <div class="water-surface-modern">
                              <div class="wave wave-1"></div>
                              <div class="wave wave-2"></div>
                              <div class="wave-3"></div>
                              <div class="wave-4"></div>
                              <div class="wave-5"></div>
                              <!-- 增强的水面反光 -->
                              <div class="water-reflection"></div>
                              <!-- 增强的水面涟漪 -->
                              <div class="water-ripples-surface">
                                <div class="ripple-circle"></div>
                                <div class="ripple-circle"></div>
                                <div class="ripple-circle"></div>
                                <div class="ripple-circle"></div>
                              </div>
                            </div>

                          </div>
                        </div>

                        <!-- 中心数据显示 -->
                        <div class="dashboard-content">
                          <div class="percentage-display">{{ gate.percentage }}%</div>
                          <div class="status-badge" :class="getRiskLevelClass(gate.percentage)">
                            {{ getRiskLevelText(gate.percentage) }}
                          </div>
                        </div>

                        <!-- 悬浮详情信息 -->
                        <div class="hover-details">
                          <div class="detail-row">
                            <span class="label">当前</span>
                            <span class="value">{{ gate.currentHeight }}m</span>
                          </div>
                          <div class="detail-row">
                            <span class="label">最大</span>
                            <span class="value">{{ gate.maxHeight }}m</span>
                          </div>
                          <div class="detail-row">
                            <span class="label">余量</span>
                            <span class="value safety-margin">{{ (gate.maxHeight - gate.currentHeight).toFixed(1) }}m</span>
                          </div>
                        </div>
                      </div>

                      <!-- 闸门名称 -->
                      <div class="modern-gate-name">{{ gate.name }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 添加折叠/展开按钮 -->
      <div class="collapse-button" @click="togglePanel">
        <img
          :src="rightIcon"
          alt="折叠/展开"
          :class="{ rotated: isPanelCollapsed }"
        />
      </div>
    </div>
  </template>

  <script setup>
  import { storeToRefs } from "pinia";
  import * as echarts from "echarts";
  import rightIcon from "@/views/screen/image/right.png";
  import updateIcon from "@/assets/images/update-icon.svg";
  import { getAreaRainProcess, getTownRainInfo } from "@/api/screen";
  import { selectStlyList, selectRvList } from "@/api/watershed/ads";
  import moment from "moment";
  import { weatherService } from "@/utils/weatherService";
  import useScreenStore from "@/store/modules/screen";

  defineOptions({
    name: "TechPanel",
  });

  const screenStore = useScreenStore();
  const currentTime = ref("");
  const rainChartRef = ref(null);
  const riverChartRef = ref(null);
  let rainChart = null;
  let riverChart = null;

  const { isPanelCollapsed } = storeToRefs(screenStore);

  // 使用 store 中的状态和方法
  const togglePanel = () => {
    screenStore.togglePanel();
    // 在状态改变后重新调整图表大小
    setTimeout(() => {
      handleResize();
    }, 300);
  };

  // 天气数据
  const weatherData = ref({
    rainfall: 0,
    forecast: [
      { day: "今天", weather: "cloudy", temp: 26 },
      { day: "明天", weather: "rain", temp: 23 },
      { day: "后天", weather: "sunny", temp: 27 },
    ],
  });

  // 乡镇降雨数据
  const rainData = ref([]);

  // 闸门安全数据
  const gateSafetyData = ref([
    { name: "1#闸", percentage: 30, maxHeight: 10.5, currentHeight: 6.8 },
    { name: "2#闸", percentage: 78, maxHeight: 12.0, currentHeight: 9.4 },
    { name: "3#闸", percentage: 45, maxHeight: 11.2, currentHeight: 5.0 },
    { name: "4#闸", percentage: 92, maxHeight: 9.8, currentHeight: 9.0 },
    { name: "5#闸", percentage: 58, maxHeight: 10.8, currentHeight: 6.3 }
  ]);

  /**
   * 获取风险等级文本
   * @param {number} percentage - 水位百分比
   * @returns {string} 风险等级文本
   */
  const getRiskLevelText = (percentage) => {
    if (percentage <= 30) {
      return "低风险";
    } else if (percentage <= 50) {
      return "中风险";
    } else if (percentage <= 70) {
      return "中高风险";
    } else {
      return "高风险";
    }
  };

  /**
   * 获取风险等级CSS类名
   * @param {number} percentage - 水位百分比
   * @returns {string} CSS类名
   */
  const getRiskLevelClass = (percentage) => {
    if (percentage <= 30) {
      return "risk-low";
    } else if (percentage <= 50) {
      return "risk-medium";
    } else if (percentage <= 70) {
      return "risk-high";
    } else {
      return "risk-danger";
    }
  };

  /**
   * 获取整体安全状态
   * @returns {string} 整体安全状态CSS类名
   */
  const getOverallSafetyStatus = () => {
    const highRiskCount = gateSafetyData.value.filter(gate => gate.percentage > 70).length;
    const mediumRiskCount = gateSafetyData.value.filter(gate => gate.percentage > 50 && gate.percentage <= 70).length;

    if (highRiskCount > 0) {
      return "status-danger";
    } else if (mediumRiskCount > 2) {
      return "status-warning";
    } else if (mediumRiskCount > 0) {
      return "status-caution";
    } else {
      return "status-safe";
    }
  };

  /**
   * 获取整体安全状态文本
   * @returns {string} 整体安全状态文本
   */
  const getOverallSafetyText = () => {
    const highRiskCount = gateSafetyData.value.filter(gate => gate.percentage > 70).length;
    const mediumRiskCount = gateSafetyData.value.filter(gate => gate.percentage > 50 && gate.percentage <= 70).length;

    if (highRiskCount > 0) {
      return "需要关注";
    } else if (mediumRiskCount > 2) {
      return "警戒状态";
    } else if (mediumRiskCount > 0) {
      return "正常运行";
    } else {
      return "运行良好";
    }
  };

    /**
   * 处理闸门点击事件
   * @param {Object} gate - 闸门数据
   */
  const handleGateClick = (gate) => {
    // 这里可以添加点击闸门后的处理逻辑
    // 比如显示详细信息、跳转到详情页面等
    console.log("点击了闸门:", gate.name, "水位:", gate.percentage + "%");

    // 可以触发一个事件通知父组件
    // emit('gate-selected', gate);
  };

  /**
   * 获取风险等级对应的颜色
   * @param {number} percentage - 水位百分比
   * @returns {string} 颜色值
   */
  const getRiskColor = (percentage) => {
    if (percentage <= 30) {
      return "#4CAF50";
    } else if (percentage <= 50) {
      return "#FF9800";
    } else if (percentage <= 70) {
      return "#FF5722";
    } else {
      return "#F44336";
    }
  };

  // 时段选项
  // 乡镇降雨
  const activeTimeRange = ref("last24h");
  // 区域天气
  const activeWeatherTimeRange = ref("last24h");
  const timeRangeOptions = ref([
    { value: "last24h", label: "最近24小时" },
    { value: "last48h", label: "最近48小时" },
    { value: "today8", label: "今日8时起" },
    { value: "yesterday8", label: "昨日8时起" },
  ]);

  /**
   * 获取天气状态对应的图标
   * @description 根据天气状态返回对应的图标URL，使用Flaticon提供的免费天气图标
   * @param {string} weather - 天气状态，支持以下值：
   *   - sunny: 晴天
   *   - cloudy: 多云
   *   - rain: 雨天
   *   - snow: 雪天
   *   - windy: 有风
   *   - fog: 雾/霾
   *   - cold: 寒冷
   *   - unknown: 未知天气
   * @returns {string} 天气图标的URL地址，如果天气状态未定义则返回晴天图标
   */
  const getWeatherIcon = (weather) => {
    const icons = {
      sunny: "https://cdn-icons-png.flaticon.com/512/3222/3222800.png",
      cloudy: "https://cdn-icons-png.flaticon.com/512/414/414927.png",
      rain: "https://cdn-icons-png.flaticon.com/512/4088/4088981.png",
      snow: "https://cdn-icons-png.flaticon.com/512/2315/2315309.png",
      windy: "https://cdn-icons-png.flaticon.com/512/3944/3944594.png",
      fog: "https://cdn-icons-png.flaticon.com/512/2952/2952824.png",
      cold: "https://cdn-icons-png.flaticon.com/512/2322/2322701.png",
      unknown: "https://cdn-icons-png.flaticon.com/512/1163/1163624.png",
    };
    return icons[weather] || icons.sunny;
  };

  // 格式化时间
  const formatTime = (date) => {
    return moment(date).format("YYYY-MM-DD HH:mm:ss");
  };

  // 获取时间范围
  const getTimeRange = (type) => {
    const now = moment();
    const endTime = formatTime(now);
    let startTime;

    switch (type) {
      case "last24h":
        startTime = formatTime(now.subtract(24, "hours"));
        break;
      case "last48h":
        startTime = formatTime(now.subtract(48, "hours"));
        break;
      case "today8":
        startTime = formatTime(now.startOf("day").hour(8));
        break;
      case "yesterday8":
        startTime = formatTime(now.subtract(1, "day").startOf("day").hour(8));
        break;
      default:
        startTime = formatTime(now.subtract(24, "hours"));
    }

    return { startTime, endTime };
  };

  // 获取天气数据
  const fetchWeatherData = async () => {
    const locationId = "101090214";

    // 并行获取所有天气数据
    const forecast = await weatherService.getWeatherForecast(locationId);

    // 更新天气预报
    if (forecast.code === "200" && forecast.daily) {
      weatherData.value.forecast = forecast.daily
        .slice(0, 3)
        .map((day, index) => {
          let dayText = "";
          if (index === 0) {
            dayText = "今天";
          } else if (index === 1) {
            dayText = "明天";
          } else if (index === 2) {
            dayText = "后天";
          }
          return {
            day: dayText,
            weather: weatherService.getWeatherIcon(day.iconDay),
            temp: day.tempMax,
          };
        });
    }
  };
  // 获取区域降雨数据
  const fetchAreaRainData = async () => {
    try {
      const { startTime, endTime } = getTimeRange(activeWeatherTimeRange.value);
      const response = await getAreaRainProcess({ startTime, endTime });

      if (response.code === 200 && response.data) {
        const { rainfall, frameList } = response.data;
        // 保留两位小数
        weatherData.value.rainfall = rainfall.toFixed(2);

        // 更新图表数据
        initRainChart(frameList);
      } else {
        console.error("获取区域降雨数据失败:", response.msg);
      }
    } catch (error) {
      console.error("获取区域降雨数据异常:", error);
    }
  };

  // 获取乡镇降雨数据
  const fetchTownRainData = async () => {
    try {
      const { startTime, endTime } = getTimeRange(activeWeatherTimeRange.value);
      const res = await getTownRainInfo({ startTime, endTime });

      if (res.code === 200 && res.rows) {
        // 按降雨量降序排序
        const sortedRows = [...res.rows].sort((a, b) => b.rainfall - a.rainfall);

        rainData.value = sortedRows;
      } else {
        console.error("获取乡镇降雨数据失败:", res.msg);
      }
    } catch (error) {
      console.error("获取乡镇降雨数据异常:", error);
    }
  };

  // 更新当前日期和时间
  const updateDateTime = () => {
    const now = moment();
    currentTime.value = now.format("MM-DD HH:mm");
  };

  // 更新闸门安全数据（模拟实时数据）
  const updateGateSafetyData = () => {
    gateSafetyData.value = gateSafetyData.value.map(gate => {
      // 模拟水位变化，在原有基础上随机增减
      const change = (Math.random() - 0.5) * 10; // -5% 到 +5% 的随机变化
      let newPercentage = gate.percentage + change;

      // 确保百分比在合理范围内
      newPercentage = Math.max(20, Math.min(95, newPercentage));

      // 根据新的百分比计算当前水位高度
      const newCurrentHeight = (newPercentage / 100) * gate.maxHeight;

      return {
        ...gate,
        percentage: Math.round(newPercentage),
        currentHeight: parseFloat(newCurrentHeight.toFixed(1))
      };
    });
  };

  // 监听时段变化
  // const changeTimeRange = () => {
  //   fetchTownRainData();
  // };
  // 监听时段变化
  const changeWeatherTimeRange = () => {
    fetchAreaRainData();
    fetchTownRainData();
  };

  // 初始化雨情量图表
  const initRainChart = (frameList) => {
    if (!rainChartRef.value) return;
    if (!frameList.length) return;

    const container = rainChartRef.value;
    rainChart = echarts.init(container);
    rainChart && rainChart.resize();

    const option = {
      grid: {
        top: 24,
        left: 40,
        right: 40,
        bottom: 20,
      },
      tooltip: {
        trigger: "axis",
        confine: true, // 确保tooltip不会超出容器边界
        position: function (point, params, dom, rect, size) {
          // 计算tooltip的位置，确保其完全可见
          const contentSize = size.contentSize;
          const viewSize = size.viewSize;
          let posX = point[0];
          let posY = point[1];

          // 如果tooltip超出右边界，将其向左移动
          if (posX + contentSize[0] > viewSize[0]) {
            posX = posX - contentSize[0] - 20;
          }
          // 如果tooltip超出左边界，将其向右移动
          if (posX < 0) {
            posX = 20;
          }
          // 如果tooltip超出上边界，将其向下移动
          if (posY < 0) {
            posY = 20;
          }
          // 如果tooltip超出下边界，将其向上移动
          if (posY + contentSize[1] > viewSize[1]) {
            posY = viewSize[1] - contentSize[1] - 20;
          }

          return [posX, posY];
        },
        axisPointer: {
          type: "line",
          lineStyle: {
            color: "rgba(255, 255, 255, 0.2)",
            width: 1,
            type: "solid",
          },
        },
        backgroundColor: "rgba(13, 47, 102, 0.9)",
        borderColor: "rgba(45, 115, 193, 0.5)",
        borderWidth: 1,
        padding: [8, 12],
        textStyle: {
          color: "#fff",
          fontSize: 12,
        },
        formatter: function (params) {
          let result = `<div style="font-size: 11px; color: rgba(255,255,255,0.7);">${params[0].axisValue}</div>`;
          // 对params进行排序，确保降雨量在前，累计降雨量在后
          params.sort((a, b) => {
            const order = { "降雨量(mm)": 1, "累计降雨量(mm)": 2 };
            return order[a.seriesName] - order[b.seriesName];
          });

          params.forEach((param) => {
            const color =
              param.seriesType === "bar"
                ? "linear-gradient(to bottom, #00c6ff, #0072ff)"
                : param.color;
            const value = param.value;
            const marker = `<span style="display:inline-block;margin-right:4px;border-radius:50%;width:6px;height:6px;background:${color};"></span>`;
            result += `<div style="margin-top: 4px; font-size: 12px; display: flex; justify-content: space-between; align-items: center;">
                  <span style="display: flex; align-items: center;">${marker}${param.seriesName}</span>
                  <span style="font-weight:bold;margin-left:12px;">${value}</span>
                </div>`;
          });
          return result;
        },
        extraCssText:
          "box-shadow: 0 0 10px rgba(0,0,0,0.3); border-radius: 4px; backdrop-filter: blur(4px); z-index: 100;",
      },
      xAxis: {
        type: "category",
        data: [],
        axisLine: {
          lineStyle: {
            color: "rgba(255, 255, 255, 0.3)",
          },
        },
        axisLabel: {
          color: "rgba(255, 255, 255, 0.7)",
          fontSize: 10,
        },
      },
      yAxis: [
        {
          type: "value",
          name: "降雨量(mm)",
          boundaryGap: ["0", "12.25%"],
          nameGap: 12,
          nameTextStyle: {
            color: "rgba(255, 255, 255, 0.7)",
            fontSize: 10,
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255, 255, 255, 0.3)",
            },
          },
          splitLine: {
            lineStyle: {
              color: "rgba(255, 255, 255, 0.1)",
            },
          },
          axisLabel: {
            color: "rgba(255, 255, 255, 0.7)",
            fontSize: 10,
          },
        },
        {
          type: "value",
          name: "累计降雨量(mm)",
          boundaryGap: ["0", "12.25%"],
          nameGap: 12,
          nameTextStyle: {
            color: "rgba(255, 255, 255, 0.7)",
            fontSize: 10,
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255, 255, 255, 0.3)",
            },
          },
          splitLine: {
            show: false,
          },
          axisLabel: {
            color: "rgba(255, 255, 255, 0.7)",
            fontSize: 10,
          },
        },
      ],
      series: [
        {
          name: "降雨量",
          type: "bar",
          data: [],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "#00c6ff" },
              { offset: 1, color: "#0072ff" },
            ]),
          },
        },
        {
          name: "累计降雨量",
          type: "line",
          yAxisIndex: 1,
          data: [],
          itemStyle: {
            color: "#ff6e76",
          },
          lineStyle: {
            width: 2,
            color: "#ff6e76",
          },
          symbol: "circle",
          symbolSize: 8,
          markPoint: {
            symbolSize: 30,
            itemStyle: {
              color: "#ff6e76",
            },
            label: {
              fontSize: 12,
            },
          },
        },
      ],
    };

    // 确保数据按时间排序
    const sortedFrameList = [...frameList].sort(
      (a, b) => moment(a.time).valueOf() - moment(b.time).valueOf()
    );

    option.series[0].data = sortedFrameList.map((item) =>
      Number(item.rainfall.toFixed(2))
    );
    option.series[1].data = sortedFrameList.map((item, index) => {
      return Number(
        sortedFrameList
          .slice(0, index + 1)
          .reduce((sum, curr) => sum + curr.rainfall, 0)
          .toFixed(2)
      );
    });
    option.xAxis.data = sortedFrameList.map((item) => {
      return moment(item.time).format("MM-DD HH:mm");
    });

    rainChart.setOption(option);
  };

  // 初始化河流水情图表
  const initRiverChart = () => {
    if (!riverChartRef.value) return;

    // 确保容器尺寸
    const container = riverChartRef.value;

    riverChart = echarts.init(container);
    riverChart && riverChart.resize();

    // 扩展到10个点，但只在5个关键点显示标签和tooltip
    const stationNames = [
      "",
      "5#闸",
      "",
      "4#闸",
      "",
      "3#闸",
      "",
      "2#闸",
      "1#闸",
      "",
    ]; // 移除数字标记
    const mainStations = [1, 3, 5, 7, 8]; // 对应'5#闸', '4#闸', '3#闸', '2#闸', "1#闸"的索引

    // 实际水位数据（单位：米）- 从上游到下游逐渐降低，模拟自然河道起伏变化
    const realTimeData = [
      3655.2, 3654.7, 3654.3, 3653.6, 3653.8, 3653.1, 3652.5, 3651.9, 3651.2,
      3650.8,
    ];
    // 左堤高程（单位：米）- 模拟左岸堤防高程，有一定起伏变化
    const leftBankData = [
      3658.5, 3657.8, 3657.7, 3656.2, 3657.1, 3656.0, 3655.8, 3654.5, 3653.5,
      3652.6,
    ];
    // 右堤高程（单位：米）- 模拟右岸堤防高程，与左岸有差异
    const rightBankData = [
      3659.2, 3658.4, 3658.5, 3657.3, 3658.2, 3656.8, 3656.5, 3655.1, 3654.0,
      3653.0,
    ];

    const option = {
      backgroundColor: "transparent",
      grid: {
        top: 25,
        left: 10,
        right: 10,
        bottom: 4,
        containLabel: true,
      },
      tooltip: {
        trigger: "item", // 改为item触发模式，只有数据点才会触发tooltip
        confine: true,
        axisPointer: {
          type: "none", // 不显示轴指示器
        },
        backgroundColor: "rgba(0, 32, 84, 0.9)",
        borderColor: "rgba(60, 141, 237, 0.7)",
        borderWidth: 1,
        padding: [8, 12],
        textStyle: {
          color: "#ffffff",
          fontSize: 11,
          lineHeight: 18,
        },
        position: function (point, params, dom, rect, size) {
          // 将tooltip放置在数据点正上方一定距离处
          return [
            point[0] - size.contentSize[0] / 2,
            point[1] - size.contentSize[1] - 20,
          ];
        },
        z: 100, // 确保tooltip总是显示在最上层
        formatter: function (params) {
          // 只对主要站点显示tooltip
          if (
            !mainStations.includes(params.dataIndex) ||
            params.seriesName !== "实时水位"
          ) {
            return null; // 返回null则不显示tooltip
          }

          // 找出当前站点的左右堤高程数据
          const leftBank = leftBankData[params.dataIndex];
          const rightBank = rightBankData[params.dataIndex];
          const waterLevel = params.value;

          // 计算水位与左右堤高程的差值
          const leftDiff = (leftBank - waterLevel).toFixed(1);
          const rightDiff = (rightBank - waterLevel).toFixed(1);

          return `<div style="font-weight:bold;color:#ffffff;margin-bottom:6px;display:flex;justify-content:space-between;">
                <span>${params.name}</span>
                <span style="color:rgba(255,255,255,0.7);font-size:11px;font-weight:normal;">${
                  currentTime.value
                }</span>
              </div>
                      <div style="display:flex;justify-content:space-between;align-items:center;margin-top:6px;">
                        <span style="display:flex;align-items:center;">
                          <span style="display:inline-block;margin-right:5px;width:8px;height:8px;border-radius:4px;background-color:#64B5F6;"></span>
                          实时水位
                        </span>
                        <span style="margin-left:15px;font-weight:bold;color:#ffffff;">${waterLevel.toFixed(
                          1
                        )} m</span>
                      </div>
                      <div style="display:flex;justify-content:space-between;align-items:center;margin-top:6px;">
                        <span style="display:flex;align-items:center;">
                          <span style="display:inline-block;margin-right:5px;width:8px;height:8px;border-radius:4px;background-color:rgba(210, 180, 140, 0.8);"></span>
                          左堤高程
                        </span>
                        <span style="margin-left:15px;font-weight:bold;color:#ffffff;">${leftBank.toFixed(
                          1
                        )} m</span>
                      </div>
                      <div style="display:flex;justify-content:space-between;align-items:center;margin-top:6px;">
                        <span style="display:flex;align-items:center;">
                          <span style="display:inline-block;margin-right:5px;width:8px;height:8px;border-radius:4px;background-color:rgba(100, 100, 100, 0.8);"></span>
                          右堤高程
                        </span>
                        <span style="margin-left:15px;font-weight:bold;color:#ffffff;">${rightBank.toFixed(
                          1
                        )} m</span>
                      </div>
                      <div style="margin-top:4px;border-top:1px dashed rgba(255,255,255,0.2);padding-top:2px;">
                        <div style="display:flex;justify-content:space-between;color:rgba(255,255,255,0.7);">
                          <span>左堤安全余量</span>
                          <span style="color:${
                            leftDiff < 2 ? "#FF6B6B" : "#4CAF50"
                          };font-weight:bold;">${leftDiff} m</span>
                        </div>
                        <div style="display:flex;justify-content:space-between;color:rgba(255,255,255,0.7);margin-top:4px;">
                          <span>右堤安全余量</span>
                          <span style="color:${
                            rightDiff < 2 ? "#FF6B6B" : "#4CAF50"
                          };font-weight:bold;">${rightDiff} m</span>
                        </div>
                      </div>`;
        },
        extraCssText:
          "box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4); border-radius: 6px; z-index: 9999 !important; backdrop-filter: blur(4px);",
      },
      xAxis: {
        type: "category",
        data: stationNames,
        boundaryGap: false,
        axisLine: {
          lineStyle: {
            color: "rgba(255, 255, 255, 0.5)",
            width: 1,
          },
        },
        axisTick: {
          show: true,
          alignWithLabel: true,
          interval: function (index, value) {
            // 只在主要站点显示刻度
            return mainStations.includes(index);
          },
          lineStyle: {
            color: "rgba(255, 255, 255, 0.3)",
          },
        },
        axisLabel: {
          color: "rgba(255, 255, 255, 0.9)",
          fontSize: 11,
          interval: function (index, value) {
            // 只显示主要站点的标签
            return mainStations.includes(index);
          },
          formatter: function (value, index) {
            // 只显示站点名称，不添加"站"字
            return value;
          },
        },
        axisPointer: {
          label: {
            show: false,
          },
        },
        splitLine: {
          show: true,
          interval: function (index, value) {
            // 只在主要站点显示分割线
            return mainStations.includes(index);
          },
          lineStyle: {
            color: "rgba(255, 255, 255, 0.1)",
            type: "dashed",
          },
        },
      },
      yAxis: {
        type: "value",
        name: "高程(m)",
        scale: true,
        nameTextStyle: {
          color: "rgba(255, 255, 255, 0.7)",
          fontSize: 10,
          padding: [0, 30, 0, 0],
          align: "left",
        },
        min: 3650,
        max: function (value) {
          return Math.ceil(value.max + 1);
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "rgba(255, 255, 255, 0.5)",
            width: 1,
          },
        },
        splitLine: {
          lineStyle: {
            color: "rgba(255, 255, 255, 0.1)",
            type: "dashed",
            width: 1,
          },
        },
        axisLabel: {
          color: "rgba(255, 255, 255, 0.9)",
          fontSize: 10,
          formatter: function (value) {
            return Number.isInteger(value) ? value.toString() : value.toFixed(1);
          },
        },
      },
      series: [
        // 左堤区域
        {
          name: "左堤高程",
          type: "line",
          data: leftBankData,
          showSymbol: false,
          smooth: true,
          smoothMonotone: "x",
          z: 1, // 降低堤岸线的z-index
          lineStyle: {
            width: 2,
            color: "rgba(210, 180, 140, 0.8)",
          },
          tooltip: {
            show: false,
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgba(210, 180, 140, 0.6)",
              },
              {
                offset: 1,
                color: "rgba(160, 120, 80, 0.7)",
              },
            ]),
            origin: "start",
            shadowColor: "rgba(210, 180, 140, 0.2)",
            shadowBlur: 10,
          },
        },
        // 右堤区域
        {
          name: "右堤高程",
          type: "line",
          data: rightBankData,
          showSymbol: false,
          smooth: true,
          smoothMonotone: "x",
          z: 1, // 降低堤岸线的z-index
          lineStyle: {
            width: 2,
            color: "rgba(100, 100, 100, 0.8)",
          },
          tooltip: {
            show: false,
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgba(100, 100, 100, 0.6)",
              },
              {
                offset: 1,
                color: "rgba(50, 50, 50, 0.7)",
              },
            ]),
            origin: "start",
            shadowColor: "rgba(0, 0, 0, 0.1)",
            shadowBlur: 10,
          },
        },
        // 实时水位
        {
          name: "实时水位",
          type: "line",
          data: realTimeData.map((val, index) => {
            return {
              value: val,
              name: stationNames[index],
            };
          }),
          z: 10, // 提高水位线的z-index，确保它在堤岸区域之上
          smooth: true,
          smoothMonotone: "x",
          symbol: function (dataIndex, params) {
            return mainStations.includes(params.dataIndex) ? "circle" : "none";
          },
          symbolSize: 8,
          sampling: "average",
          itemStyle: {
            color: "#64B5F6",
            borderWidth: 2,
            borderColor: "#ffffff",
            shadowColor: "rgba(0, 0, 0, 0.3)",
            shadowBlur: 5,
          },
          lineStyle: {
            width: 3,
            color: "#64B5F6",
            shadowColor: "rgba(0, 120, 255, 0.4)",
            shadowBlur: 10,
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgba(100, 181, 246, 0.6)", // 提高透明度
              },
              {
                offset: 0.5,
                color: "rgba(100, 181, 246, 0.4)", // 提高透明度
              },
              {
                offset: 1,
                color: "rgba(100, 181, 246, 0.2)", // 提高透明度
              },
            ]),
            origin: "start",
            shadowColor: "rgba(0, 0, 0, 0.2)",
            shadowBlur: 12,
            opacity: 1, // 完全不透明
          },
        },
      ],
    };

    riverChart.setOption(option);
  };

  // 添加流域和河流数据
  const selectedBasin = ref("");
  const selectedRiver = ref("");
  const basinOptions = ref([]);
  const riverOptions = ref([]);

  // 监听流域变化
  const handleBasinChange = (value) => {
    console.log("选择的流域:", value);
    // 根据选择的流域获取河流列表
    selectRvList({ basinId: value })
      .then((res) => {
        if (res.code === 200) {
          // 处理新的河流数据结构
          const processRiverData = (riverData) => {
            if (!riverData) return [];

            // 递归处理河流数据及其子河流
            const processRiver = (river) => {
              const { data, children } = river;
              return {
                ...data,
                children:
                  children && children.length > 0
                    ? children.map((child) => processRiver(child))
                    : [],
              };
            };

            // 处理顶层河流数据
            return Array.isArray(riverData)
              ? riverData.map((river) => processRiver(river))
              : [processRiver(riverData)];
          };

          riverOptions.value = processRiverData(res.data);
          // 重置河流选择
          selectedRiver.value = "";
        } else {
          console.error("获取河流列表失败:", res.msg);
          riverOptions.value = [];
        }
      })
      .catch((error) => {
        console.error("获取河流列表异常:", error);
        riverOptions.value = [];
      });

    // 这里可以根据选择的流域更新图表数据
    if (riverChart) {
      // 更新图表数据的逻辑
    }
  };

  // 监听河流变化
  const handleRiverChange = (value) => {
    console.log("选择的河流:", value);
    // 这里可以根据选择的河流更新图表数据
    if (riverChart) {
      // 更新图表数据的逻辑
    }
  };

  /**
   * 格式化树形数据
   * @param {Array} data - 原始树形数据
   * @returns {Array} 格式化后的树形数据
   */
  const formatTree = (data) => {
    return data.map((item) => {
      return {
        ...item.data,
        children: item.children && formatTree(item.children),
      };
    });
  };

  const initRiverData = () => {
    // 加载流域树
    selectStlyList({}).then((res) => {
      if (res.code === 200) {
        basinOptions.value = formatTree(res.data);
      }
    });
  };

  let updateInterval;
  let gateUpdateInterval;

  onMounted(async () => {
    updateDateTime();
    initRiverChart();
    initRiverData();
    // 获取初始数据
    fetchAreaRainData();
    fetchTownRainData();
    fetchWeatherData();
    // 设置定时更新
    updateInterval = setInterval(() => {
      updateDateTime();
      fetchAreaRainData();
      fetchTownRainData();
      updateGateSafetyData();
    }, 5 * 60 * 1000); // 每5分钟更新一次

    // 设置闸门安全数据更新（更频繁）
    gateUpdateInterval = setInterval(() => {
      updateGateSafetyData();
    }, 30 * 1000); // 每30秒更新一次

    window.addEventListener("resize", handleResize);

    return () => {
      clearInterval(updateInterval);
    };
  });

  onUnmounted(() => {
    window.removeEventListener("resize", handleResize);
    if (updateInterval) {
      clearInterval(updateInterval);
    }
    // 清理闸门安全数据更新定时器
    if (gateUpdateInterval) {
      clearInterval(gateUpdateInterval);
    }
    if (rainChart) {
      rainChart.dispose();
    }
    if (riverChart) {
      riverChart.dispose();
    }
  });

  const handleResize = () => {
    if (rainChart) {
      try {
        rainChart.resize();
      } catch (e) {
        console.error("雨情图表调整大小失败", e);
      }
    }
    if (riverChart) {
      try {
        riverChart.resize();
      } catch (e) {
        console.error("河流图表调整大小失败", e);
      }
    }
  };
  </script>

  <style lang="scss">
  // 将下拉菜单样式移到全局作用域
  .el-popper.is-pure.dark-select-dropdown {
    background: rgba(14, 74, 131, 0.95) !important;
    border: 1px solid rgba(45, 115, 193, 0.5) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;

    .el-select-dropdown__wrap {
      max-height: 200px;
    }

    .el-select-dropdown__list {
      padding: 4px 0;
      background: transparent !important;
    }

    .el-select-dropdown__item,
    .el-tree,
    .el-tree-node__content {
      color: rgba(255, 255, 255, 0.9) !important;
      padding: 0 8px;
      height: 28px;
      line-height: 28px;
      font-size: 13px;
      background: transparent !important;

      &.hover,
      &:hover {
        background: rgba(21, 101, 192, 0.2) !important;
      }

      &.selected {
        background: rgba(21, 101, 192, 0.3) !important;
        color: #64b5f6 !important;
        font-weight: normal;

        &.hover,
        &:hover {
          background: rgba(21, 101, 192, 0.4) !important;
        }
      }
    }

    .el-scrollbar__view {
      padding: 4px 0;
    }

    .el-scrollbar__bar {
      background: transparent;
      width: 4px;
      right: 2px;

      &.is-vertical {
        .el-scrollbar__thumb {
          background: rgba(100, 181, 246, 0.3);
          border-radius: 2px;

          &:hover {
            background: rgba(100, 181, 246, 0.5);
          }
        }
      }
    }

    .el-popper__arrow {
      display: none !important;
    }
  }
  </style>

  <style lang="scss" scoped>
  @use "./index.scss";
  </style>

